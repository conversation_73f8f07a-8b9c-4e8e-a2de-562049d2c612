<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Company;
use App\Models\CompanyDefaultLeaveType;
use App\Models\CompanyLeaveType;
use App\Models\CompanyLeaveTypePolicy;
use App\Models\Employee;
use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use App\Models\Title;
use App\Repositories\NewCompanyLeaveTypePolicyRepository;
use App\Repositories\NewCompanyLeaveTypeRepository;
use App\Repositories\V1\Leaves\CompanyDefaultLeaveTypesRepository;
use App\Services\V1\LeaveManagement\FillEmployeeBalancesService;
use App\Traits\GenerateUuid;
use App\Util\DefaultLeaveTypesUtil;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StandardizeLeaveSystem extends Command
{
    use GenerateUuid;

    protected $signature = 'leave:standardize-system {--dry-run : Preview changes without executing them} {--company-id= : Process only specific company ID}';

    protected $description = 'Standardize leave system: ensure each company has exactly one annual leave (168 hours) and one emergency leave (48 hours) linked to annual leave';

    private NewCompanyLeaveTypeRepository $companyLeaveTypeRepository;
    private NewCompanyLeaveTypePolicyRepository $companyLeaveTypePolicyRepository;
    private CompanyDefaultLeaveTypesRepository $companyDefaultLeaveTypesRepository;
    private FillEmployeeBalancesService $fillEmployeeBalancesService;

    private array $report = [];
    private bool $isDryRun = false;

    public function __construct(
        NewCompanyLeaveTypeRepository $companyLeaveTypeRepository,
        NewCompanyLeaveTypePolicyRepository $companyLeaveTypePolicyRepository,
        CompanyDefaultLeaveTypesRepository $companyDefaultLeaveTypesRepository,
        FillEmployeeBalancesService $fillEmployeeBalancesService
    ) {
        parent::__construct();
        $this->companyLeaveTypeRepository = $companyLeaveTypeRepository;
        $this->companyLeaveTypePolicyRepository = $companyLeaveTypePolicyRepository;
        $this->companyDefaultLeaveTypesRepository = $companyDefaultLeaveTypesRepository;
        $this->fillEmployeeBalancesService = $fillEmployeeBalancesService;
    }

    public function handle()
    {
        $this->isDryRun = $this->option('dry-run');
        $companyId = $this->option('company-id');

        if ($this->isDryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
        }

        $this->info('🚀 Starting Leave System Standardization...');

        try {
            if (!$this->isDryRun) {
                DB::beginTransaction();
            }

            $companies = $this->getCompanies($companyId);
            $this->info("📊 Processing {$companies->count()} companies...");

            foreach ($companies as $company) {
                $this->processCompany($company);
            }

            if (!$this->isDryRun) {
                DB::commit();
                $this->info('✅ All changes committed successfully');
            }

            $this->displayReport();

        } catch (Exception $e) {
            if (!$this->isDryRun) {
                DB::rollBack();
            }
            
            $this->error('❌ Error occurred: ' . $e->getMessage());
            Log::error('Leave System Standardization Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }

        return 0;
    }

    private function getCompanies($companyId = null)
    {
        $query = Company::query();
        
        if ($companyId) {
            $query->where('id', $companyId);
        }
        
        return $query->get();
    }

    private function processCompany(Company $company)
    {
        $this->info("🏢 Processing Company ID: {$company->id} ({$company->name})");
        
        $this->initializeCompanyReport($company);
        
        // Step 1: Standardize Annual Leave
        $primaryAnnualLeave = $this->standardizeAnnualLeave($company);
        
        // Step 2: Clean up duplicate Annual Leaves
        $this->cleanupDuplicateAnnualLeaves($company, $primaryAnnualLeave);
        
        // Step 3: Standardize Emergency Leave
        $primaryEmergencyLeave = $this->standardizeEmergencyLeave($company, $primaryAnnualLeave);
        
        // Step 4: Clean up duplicate Emergency Leaves
        $this->cleanupDuplicateEmergencyLeaves($company, $primaryEmergencyLeave);
        
        // Step 5: Ensure employee balances exist
        $this->ensureEmployeeBalances($company, $primaryAnnualLeave, $primaryEmergencyLeave);
        
        $this->info("✅ Completed processing Company ID: {$company->id}");
    }

    private function initializeCompanyReport(Company $company)
    {
        $this->report[$company->id] = [
            'company_name' => $company->name,
            'annual_leave' => [
                'found_existing' => false,
                'created_new' => false,
                'duplicates_removed' => 0,
                'requests_redirected' => 0,
                'balances_redirected' => 0,
                'titles_redirected' => 0
            ],
            'emergency_leave' => [
                'found_existing' => false,
                'created_new' => false,
                'linked_to_annual' => false,
                'duplicates_removed' => 0,
                'requests_redirected' => 0,
                'balances_redirected' => 0,
                'titles_redirected' => 0
            ],
            'employee_balances' => [
                'annual_balances_created' => 0,
                'emergency_balances_created' => 0
            ]
        ];
    }

    private function standardizeAnnualLeave(Company $company): CompanyLeaveType
    {
        $this->info("  📅 Standardizing Annual Leave for Company {$company->id}...");
        
        // Check if company already has annual_leave_id set and the leave type exists
        if ($company->annual_leave_id) {
            $existingAnnualLeave = CompanyLeaveType::where('id', $company->annual_leave_id)
                ->where('company_id', $company->id)
                ->whereNull('deleted_at')
                ->first();
                
            if ($existingAnnualLeave) {
                $this->info("    ✓ Found existing annual leave type (ID: {$existingAnnualLeave->id})");
                $this->report[$company->id]['annual_leave']['found_existing'] = true;
                
                // Ensure policy exists and has correct value
                $this->ensureAnnualLeavePolicy($company, $existingAnnualLeave);
                $this->ensureCompanyDefaultLeaveType($company, $existingAnnualLeave, 'annual_leave_type_id');
                
                return $existingAnnualLeave;
            }
        }
        
        // Create new annual leave type
        $this->info("    🆕 Creating new annual leave type...");
        $annualLeave = $this->createAnnualLeaveType($company);
        $this->report[$company->id]['annual_leave']['created_new'] = true;
        
        // Update company's annual_leave_id
        if (!$this->isDryRun) {
            $company->update(['annual_leave_id' => $annualLeave->id]);
        }
        
        return $annualLeave;
    }

    private function createAnnualLeaveType(Company $company): CompanyLeaveType
    {
        $data = [
            'company_id' => $company->id,
            'name_ar' => 'إجازة سنوية',
            'name_en' => 'Annual Leave',
            'name' => 'annual_leave_' . uniqid(),
            'is_primary' => 1,
            'balance_period' => 'calendar_year',
            'gender' => 'all',
            'uuid' => $this->Uuid7(),
        ];
        
        if ($this->isDryRun) {
            $this->info("    [DRY RUN] Would create annual leave type with data: " . json_encode($data));
            // Return a mock object for dry run
            $mockLeave = new CompanyLeaveType();
            $mockLeave->id = 999999; // Mock ID
            $mockLeave->company_id = $company->id;
            return $mockLeave;
        }
        
        $annualLeave = $this->companyLeaveTypeRepository->add($data);
        
        // Create policy
        $this->ensureAnnualLeavePolicy($company, $annualLeave);
        
        // Create default leave type record
        $this->ensureCompanyDefaultLeaveType($company, $annualLeave, 'annual_leave_type_id');
        
        return $annualLeave;
    }

    private function ensureAnnualLeavePolicy(Company $company, CompanyLeaveType $annualLeave)
    {
        $policy = $annualLeave->companyLeaveTypePolicy;
        
        if (!$policy) {
            $this->info("    🔧 Creating annual leave policy...");
            $policyData = [
                'company_id' => $company->id,
                'company_leave_type_id' => $annualLeave->id,
                'base_balance' => 168, // 21 days * 8 hours
                'unit' => 'hours',
                'is_probation_allowed' => 0,
                'request_before_days' => 0,
                'min_requester_years' => 0,
            ];
            
            if (!$this->isDryRun) {
                $policy = $this->companyLeaveTypePolicyRepository->add($policyData);
                $this->attachAllTitlesToPolicy($company, $policy);
            } else {
                $this->info("    [DRY RUN] Would create policy with data: " . json_encode($policyData));
            }
        } else {
            // Update policy to ensure correct base_balance
            if ($policy->base_balance != 168) {
                $this->info("    🔧 Updating annual leave policy base_balance from {$policy->base_balance} to 168...");
                if (!$this->isDryRun) {
                    $policy->update(['base_balance' => 168]);
                }
            }
        }
    }

    private function ensureCompanyDefaultLeaveType(Company $company, CompanyLeaveType $leaveType, string $key)
    {
        $defaultLeaveType = CompanyDefaultLeaveType::where('company_id', $company->id)
            ->where('key', $key)
            ->first();
            
        if (!$defaultLeaveType) {
            $this->info("    🔧 Creating company default leave type record for key: {$key}...");
            $data = [
                'company_id' => $company->id,
                'company_leave_type_id' => $leaveType->id,
                'key' => $key,
            ];
            
            if (!$this->isDryRun) {
                $this->companyDefaultLeaveTypesRepository->add($data);
            } else {
                $this->info("    [DRY RUN] Would create default leave type with data: " . json_encode($data));
            }
        } else if ($defaultLeaveType->company_leave_type_id != $leaveType->id) {
            $this->info("    🔧 Updating company default leave type to point to correct leave type...");
            if (!$this->isDryRun) {
                $defaultLeaveType->update(['company_leave_type_id' => $leaveType->id]);
            }
        }
    }

    private function attachAllTitlesToPolicy(Company $company, CompanyLeaveTypePolicy $policy)
    {
        $titleIds = Title::where('company_id', $company->id)->pluck('id')->toArray();
        if (!empty($titleIds)) {
            $policy->titles()->attach($titleIds);
        }
    }

    private function cleanupDuplicateAnnualLeaves(Company $company, CompanyLeaveType $primaryAnnualLeave)
    {
        $this->info("  🧹 Cleaning up duplicate annual leaves for Company {$company->id}...");

        // Find all other annual leave types for this company
        $duplicateAnnualLeaves = CompanyLeaveType::where('company_id', $company->id)
            ->where('id', '!=', $primaryAnnualLeave->id)
            ->whereNull('deleted_at')
            ->where(function ($query) {
                $query->where('name_en', 'LIKE', '%annual%')
                    ->orWhere('name_ar', 'LIKE', '%سنوية%')
                    ->orWhere('name_en', 'LIKE', '%Annual%')
                    ->orWhere('name_ar', 'LIKE', '%إجازة سنوية%');
            })
            ->get();

        if ($duplicateAnnualLeaves->isEmpty()) {
            $this->info("    ✓ No duplicate annual leaves found");
            return;
        }

        $this->info("    🔍 Found {$duplicateAnnualLeaves->count()} duplicate annual leave types");

        foreach ($duplicateAnnualLeaves as $duplicateLeave) {
            $this->redirectLeaveRequestsAndBalances($company, $duplicateLeave, $primaryAnnualLeave, 'annual_leave');
            $this->softDeleteLeaveTypeAndPolicy($company, $duplicateLeave, 'annual_leave');
        }
    }

    private function redirectLeaveRequestsAndBalances(Company $company, CompanyLeaveType $fromLeave, CompanyLeaveType $toLeave, string $leaveTypeKey)
    {
        // Redirect employee leave requests
        $requestsCount = EmployeeLeaveRequest::where('company_leave_type_id', $fromLeave->id)->count();
        if ($requestsCount > 0) {
            $this->info("    🔄 Redirecting {$requestsCount} leave requests from {$fromLeave->id} to {$toLeave->id}...");
            if (!$this->isDryRun) {
                EmployeeLeaveRequest::where('company_leave_type_id', $fromLeave->id)
                    ->update([
                        'company_leave_type_id' => $toLeave->id,
                        'company_leave_type_policy_id' => $toLeave->companyLeaveTypePolicy->id ?? null
                    ]);
            }
            $this->report[$company->id][$leaveTypeKey]['requests_redirected'] += $requestsCount;
        }

        // Soft delete employee leave balances (as per requirements)
        $balancesCount = EmployeeLeaveBalance::where('company_leave_type_id', $fromLeave->id)->count();
        if ($balancesCount > 0) {
            $this->info("    🗑️ Soft deleting {$balancesCount} leave balances for leave type {$fromLeave->id}...");
            if (!$this->isDryRun) {
                EmployeeLeaveBalance::where('company_leave_type_id', $fromLeave->id)->delete();
            }
            $this->report[$company->id][$leaveTypeKey]['balances_redirected'] += $balancesCount;
        }

        // Redirect title associations from old policy to new policy
        $oldPolicy = $fromLeave->companyLeaveTypePolicy;
        $newPolicy = $toLeave->companyLeaveTypePolicy;

        if ($oldPolicy && $newPolicy) {
            $titleIds = $oldPolicy->titles()->pluck('title_id')->toArray();
            if (!empty($titleIds)) {
                $this->info("    🔄 Redirecting {count($titleIds)} title associations...");
                if (!$this->isDryRun) {
                    $newPolicy->titles()->syncWithoutDetaching($titleIds);
                }
                $this->report[$company->id][$leaveTypeKey]['titles_redirected'] += count($titleIds);
            }
        }
    }

    private function softDeleteLeaveTypeAndPolicy(Company $company, CompanyLeaveType $leaveType, string $leaveTypeKey)
    {
        $this->info("    🗑️ Soft deleting leave type {$leaveType->id} and its policy...");

        if (!$this->isDryRun) {
            // Soft delete the policy first
            if ($leaveType->companyLeaveTypePolicy) {
                $leaveType->companyLeaveTypePolicy->delete();
            }

            // Soft delete any company default leave type records
            CompanyDefaultLeaveType::where('company_leave_type_id', $leaveType->id)->delete();

            // Soft delete the leave type itself
            $leaveType->delete();
        }

        $this->report[$company->id][$leaveTypeKey]['duplicates_removed']++;
    }

    private function standardizeEmergencyLeave(Company $company, CompanyLeaveType $primaryAnnualLeave): ?CompanyLeaveType
    {
        $this->info("  🚨 Standardizing Emergency Leave for Company {$company->id}...");

        // First, try to find emergency leave linked to the annual leave
        $linkedEmergencyLeave = CompanyLeaveType::where('company_id', $company->id)
            ->where('secondary_company_leave_type_id', $primaryAnnualLeave->id)
            ->whereNull('deleted_at')
            ->where(function ($query) {
                $query->where('name_en', 'LIKE', '%emergency%')
                    ->orWhere('name_ar', 'LIKE', '%طارئ%')
                    ->orWhere('name_en', 'LIKE', '%Emergency%')
                    ->orWhere('name_ar', 'LIKE', '%عارضة%');
            })
            ->first();

        if ($linkedEmergencyLeave) {
            $this->info("    ✓ Found emergency leave linked to annual leave (ID: {$linkedEmergencyLeave->id})");
            $this->report[$company->id]['emergency_leave']['found_existing'] = true;
            $this->ensureEmergencyLeavePolicy($company, $linkedEmergencyLeave);
            $this->ensureCompanyDefaultLeaveType($company, $linkedEmergencyLeave, DefaultLeaveTypesUtil::EMERGENCY_LEAVE);
            return $linkedEmergencyLeave;
        }

        // Try to find existing emergency leave (not linked to annual)
        $existingEmergencyLeaves = CompanyLeaveType::where('company_id', $company->id)
            ->whereNull('deleted_at')
            ->where(function ($query) {
                $query->where('name_en', 'LIKE', '%emergency%')
                    ->orWhere('name_ar', 'LIKE', '%طارئ%')
                    ->orWhere('name_en', 'LIKE', '%Emergency%')
                    ->orWhere('name_ar', 'LIKE', '%عارضة%');
            })
            ->get();

        if ($existingEmergencyLeaves->isNotEmpty()) {
            // Prefer one that has a company default leave type record
            $preferredEmergencyLeave = null;
            foreach ($existingEmergencyLeaves as $emergencyLeave) {
                $hasDefaultRecord = CompanyDefaultLeaveType::where('company_id', $company->id)
                    ->where('company_leave_type_id', $emergencyLeave->id)
                    ->where('key', DefaultLeaveTypesUtil::EMERGENCY_LEAVE)
                    ->exists();

                if ($hasDefaultRecord) {
                    $preferredEmergencyLeave = $emergencyLeave;
                    break;
                }
            }

            $selectedEmergencyLeave = $preferredEmergencyLeave ?? $existingEmergencyLeaves->first();

            $this->info("    🔗 Linking existing emergency leave (ID: {$selectedEmergencyLeave->id}) to annual leave...");
            if (!$this->isDryRun) {
                $selectedEmergencyLeave->update([
                    'secondary_company_leave_type_id' => $primaryAnnualLeave->id,
                    'is_primary' => 0
                ]);
            }

            $this->report[$company->id]['emergency_leave']['found_existing'] = true;
            $this->report[$company->id]['emergency_leave']['linked_to_annual'] = true;

            $this->ensureEmergencyLeavePolicy($company, $selectedEmergencyLeave);
            $this->ensureCompanyDefaultLeaveType($company, $selectedEmergencyLeave, DefaultLeaveTypesUtil::EMERGENCY_LEAVE);

            return $selectedEmergencyLeave;
        }

        // Create new emergency leave
        $this->info("    🆕 Creating new emergency leave type...");
        $emergencyLeave = $this->createEmergencyLeaveType($company, $primaryAnnualLeave);
        $this->report[$company->id]['emergency_leave']['created_new'] = true;

        return $emergencyLeave;
    }

    private function createEmergencyLeaveType(Company $company, CompanyLeaveType $primaryAnnualLeave): CompanyLeaveType
    {
        $data = [
            'company_id' => $company->id,
            'name_ar' => 'إجازة طارئة',
            'name_en' => 'Emergency Leave',
            'name' => 'emergency_leave_' . uniqid(),
            'is_primary' => 0,
            'secondary_company_leave_type_id' => $primaryAnnualLeave->id,
            'balance_period' => 'calendar_year',
            'gender' => 'all',
            'uuid' => $this->Uuid7(),
        ];

        if ($this->isDryRun) {
            $this->info("    [DRY RUN] Would create emergency leave type with data: " . json_encode($data));
            $mockLeave = new CompanyLeaveType();
            $mockLeave->id = 999998; // Mock ID
            $mockLeave->company_id = $company->id;
            return $mockLeave;
        }

        $emergencyLeave = $this->companyLeaveTypeRepository->add($data);

        // Create policy
        $this->ensureEmergencyLeavePolicy($company, $emergencyLeave);

        // Create default leave type record
        $this->ensureCompanyDefaultLeaveType($company, $emergencyLeave, DefaultLeaveTypesUtil::EMERGENCY_LEAVE);

        return $emergencyLeave;
    }

    private function ensureEmergencyLeavePolicy(Company $company, CompanyLeaveType $emergencyLeave)
    {
        $policy = $emergencyLeave->companyLeaveTypePolicy;

        if (!$policy) {
            $this->info("    🔧 Creating emergency leave policy...");
            $policyData = [
                'company_id' => $company->id,
                'company_leave_type_id' => $emergencyLeave->id,
                'base_balance' => 48, // 6 days * 8 hours
                'unit' => 'hours',
                'is_probation_allowed' => 0,
                'request_before_days' => 0,
                'min_requester_years' => 0,
            ];

            if (!$this->isDryRun) {
                $policy = $this->companyLeaveTypePolicyRepository->add($policyData);
                $this->attachAllTitlesToPolicy($company, $policy);
            } else {
                $this->info("    [DRY RUN] Would create policy with data: " . json_encode($policyData));
            }
        } else {
            // Update policy to ensure correct base_balance
            if ($policy->base_balance != 48) {
                $this->info("    🔧 Updating emergency leave policy base_balance from {$policy->base_balance} to 48...");
                if (!$this->isDryRun) {
                    $policy->update(['base_balance' => 48]);
                }
            }
        }
    }

    private function cleanupDuplicateEmergencyLeaves(Company $company, ?CompanyLeaveType $primaryEmergencyLeave)
    {
        if (!$primaryEmergencyLeave) {
            return;
        }

        $this->info("  🧹 Cleaning up duplicate emergency leaves for Company {$company->id}...");

        // Find all other emergency leave types for this company
        $duplicateEmergencyLeaves = CompanyLeaveType::where('company_id', $company->id)
            ->where('id', '!=', $primaryEmergencyLeave->id)
            ->whereNull('deleted_at')
            ->where(function ($query) {
                $query->where('name_en', 'LIKE', '%emergency%')
                    ->orWhere('name_ar', 'LIKE', '%طارئ%')
                    ->orWhere('name_en', 'LIKE', '%Emergency%')
                    ->orWhere('name_ar', 'LIKE', '%عارضة%');
            })
            ->get();

        if ($duplicateEmergencyLeaves->isEmpty()) {
            $this->info("    ✓ No duplicate emergency leaves found");
            return;
        }

        $this->info("    🔍 Found {$duplicateEmergencyLeaves->count()} duplicate emergency leave types");

        foreach ($duplicateEmergencyLeaves as $duplicateLeave) {
            $this->redirectLeaveRequestsAndBalances($company, $duplicateLeave, $primaryEmergencyLeave, 'emergency_leave');
            $this->softDeleteLeaveTypeAndPolicy($company, $duplicateLeave, 'emergency_leave');
        }
    }

    private function ensureEmployeeBalances(Company $company, CompanyLeaveType $primaryAnnualLeave, ?CompanyLeaveType $primaryEmergencyLeave)
    {
        $this->info("  👥 Ensuring employee leave balances for Company {$company->id}...");

        // Get all non-terminated employees
        $employees = Employee::where('company_id', $company->id)
            ->where('status', '!=', 'terminated')
            ->with('employeeLeaveBalances')
            ->get();

        if ($employees->isEmpty()) {
            $this->info("    ✓ No active employees found");
            return;
        }

        $this->info("    👥 Processing {$employees->count()} active employees...");

        foreach ($employees as $employee) {
            $this->ensureEmployeeAnnualLeaveBalance($company, $employee, $primaryAnnualLeave);

            if ($primaryEmergencyLeave) {
                $this->ensureEmployeeEmergencyLeaveBalance($company, $employee, $primaryEmergencyLeave);
            }
        }
    }

    private function ensureEmployeeAnnualLeaveBalance(Company $company, Employee $employee, CompanyLeaveType $primaryAnnualLeave)
    {
        $policy = $primaryAnnualLeave->companyLeaveTypePolicy;
        if (!$policy) {
            return;
        }

        // Check if employee already has a current balance for this leave type
        $existingBalance = EmployeeLeaveBalance::where('employee_id', $employee->id)
            ->where('company_leave_type_id', $primaryAnnualLeave->id)
            ->where('start', '<=', now())
            ->where('end', '>=', now())
            ->first();

        if (!$existingBalance) {
            $this->info("    🆕 Creating annual leave balance for employee {$employee->id}...");
            if (!$this->isDryRun) {
                // Use the FillEmployeeBalancesService to create proper balances
                $this->fillEmployeeBalancesService->fill([$employee], [$company->id], $primaryAnnualLeave->id);
            }
            $this->report[$company->id]['employee_balances']['annual_balances_created']++;
        }
    }

    private function ensureEmployeeEmergencyLeaveBalance(Company $company, Employee $employee, CompanyLeaveType $primaryEmergencyLeave)
    {
        $policy = $primaryEmergencyLeave->companyLeaveTypePolicy;
        if (!$policy) {
            return;
        }

        // Check if employee already has a current balance for this leave type
        $existingBalance = EmployeeLeaveBalance::where('employee_id', $employee->id)
            ->where('company_leave_type_id', $primaryEmergencyLeave->id)
            ->where('start', '<=', now())
            ->where('end', '>=', now())
            ->first();

        if (!$existingBalance) {
            $this->info("    🆕 Creating emergency leave balance for employee {$employee->id}...");
            if (!$this->isDryRun) {
                // Use the FillEmployeeBalancesService to create proper balances
                $this->fillEmployeeBalancesService->fill([$employee], [$company->id], $primaryEmergencyLeave->id);
            }
            $this->report[$company->id]['employee_balances']['emergency_balances_created']++;
        }
    }

    private function displayReport()
    {
        $this->info("\n📊 STANDARDIZATION REPORT");
        $this->info("=" . str_repeat("=", 50));

        foreach ($this->report as $companyId => $data) {
            $this->info("\n🏢 Company: {$data['company_name']} (ID: {$companyId})");

            // Annual Leave Report
            $annual = $data['annual_leave'];
            $this->info("  📅 Annual Leave:");
            $this->info("    • Found existing: " . ($annual['found_existing'] ? 'Yes' : 'No'));
            $this->info("    • Created new: " . ($annual['created_new'] ? 'Yes' : 'No'));
            $this->info("    • Duplicates removed: {$annual['duplicates_removed']}");
            $this->info("    • Requests redirected: {$annual['requests_redirected']}");
            $this->info("    • Balances soft deleted: {$annual['balances_redirected']}");
            $this->info("    • Titles redirected: {$annual['titles_redirected']}");

            // Emergency Leave Report
            $emergency = $data['emergency_leave'];
            $this->info("  🚨 Emergency Leave:");
            $this->info("    • Found existing: " . ($emergency['found_existing'] ? 'Yes' : 'No'));
            $this->info("    • Created new: " . ($emergency['created_new'] ? 'Yes' : 'No'));
            $this->info("    • Linked to annual: " . ($emergency['linked_to_annual'] ? 'Yes' : 'No'));
            $this->info("    • Duplicates removed: {$emergency['duplicates_removed']}");
            $this->info("    • Requests redirected: {$emergency['requests_redirected']}");
            $this->info("    • Balances soft deleted: {$emergency['balances_redirected']}");
            $this->info("    • Titles redirected: {$emergency['titles_redirected']}");

            // Employee Balances Report
            $balances = $data['employee_balances'];
            $this->info("  👥 Employee Balances:");
            $this->info("    • Annual balances created: {$balances['annual_balances_created']}");
            $this->info("    • Emergency balances created: {$balances['emergency_balances_created']}");
        }

        $this->info("\n✅ Standardization completed successfully!");

        if ($this->isDryRun) {
            $this->info("\n🔍 This was a DRY RUN - no actual changes were made.");
            $this->info("Run without --dry-run to apply these changes.");
        }
    }
}
